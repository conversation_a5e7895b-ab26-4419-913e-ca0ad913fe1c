# Klikbuy Homepage - Animation Implementation Notes

## 🎬 GSAP Animation Breakdown

### Loader Animation (src/components/Loader.js)
```javascript
// Timeline sequence
tl.to(typingRef.current, { text: "Guessing...", duration: 0.8 })
  .to(typingRef.current, { text: "", duration: 0.4 })
  .to(typingRef.current, { text: "Tracking...", duration: 0.8 })
  .to(typingRef.current, { text: "", duration: 0.4 })
  .to(typingRef.current, { text: "Clarity.", duration: 0.8 })
  .to(logoRef.current, { opacity: 1, scale: 1, duration: 1 })
  .to([logoRef.current, typingRef.current], { 
    scale: 0.3, 
    y: -window.innerHeight * 0.4,
    duration: 1 
  })
  .to(loaderRef.current, { opacity: 0, duration: 0.5 });
```

**Key Features:**
- TextPlugin untuk typing effect
- Sequential logo scaling
- Positioning transition ke navbar
- Smooth fade-out

### Hero Section (src/components/Hero.js)
```javascript
// Scroll pinning
ScrollTrigger.create({
  trigger: heroRef.current,
  start: "top top",
  end: "bottom center",
  pin: true,
  pinSpacing: false
});

// Staggered reveal
tl.to(titleRef.current, { opacity: 1, y: 0, filter: 'blur(0px)' })
  .to(subtitleRef.current, { opacity: 1, y: 0, filter: 'blur(0px)' }, "-=0.8")
  .to(ctaRef.current, { opacity: 1, y: 0, filter: 'blur(0px)' }, "-=0.6")
  .to(modelRef.current, { scale: 1, opacity: 1 }, "-=1");
```

**Key Features:**
- Scroll pinning untuk immersive experience
- Blur-to-focus transition
- 3D model scaling effect
- Layered timeline dengan overlap

### Belief Section (src/components/Belief.js)
```javascript
// Brutalist staggered animation
tl.to(titleRef.current, { opacity: 1, y: 0, duration: 1 })
  .to(beliefItemsRef.current, {
    opacity: 1, y: 0, x: 0,
    duration: 0.8, stagger: 0.2
  }, "-=0.5");
```

**Key Features:**
- Asymmetric layout animasi
- Staggered reveal dengan delay
- X-axis movement untuk depth
- Hover state dengan border accent

### Why Join Section (src/components/WhyJoin.js)
```javascript
// 3D rotation hover
gsap.to(iconEl, {
  scale: 1.1,
  rotationY: 15,
  rotationX: 5,
  duration: 0.3
});
```

**Key Features:**
- Float-in cards dengan scale
- 3D rotation pada hover
- Box-shadow animation
- Transform-gpu optimization

### Who For Section (src/components/WhoFor.js)
```javascript
// Flip-in cards
gsap.set(cardsRef.current, {
  opacity: 0, y: 60,
  rotationX: -20, scale: 0.9
});

tl.to(cardsRef.current, {
  opacity: 1, y: 0,
  rotationX: 0, scale: 1,
  duration: 1.2, stagger: 0.2,
  ease: "expo.out"
});
```

**Key Features:**
- 3D flip-in effect
- Emoji scale animation
- Elastic hover dengan rotation
- Perspective transforms

### Final CTA Section (src/components/FinalCTA.js)
```javascript
// Elastic bounce-in
tl.to(ctaRef.current, {
  opacity: 1, scale: 1, y: 0,
  duration: 1.2,
  ease: "elastic.out(1, 0.6)"
});

// Hover glow effect
gsap.to(e.target, {
  scale: 1.05, y: -4,
  boxShadow: "0 20px 40px rgba(150, 201, 255, 0.4)"
});
```

**Key Features:**
- Elastic bounce animation
- Multi-layer glow effects
- Scale hover dengan elevation
- Gradient border animation

## 🎯 Performance Optimizations

### 1. Transform-GPU Usage
```javascript
// Selalu gunakan transform-gpu untuk animasi smooth
className="transform-gpu"
style={{ perspective: "1000px" }}
```

### 2. GSAP Cleanup
```javascript
useEffect(() => {
  return () => {
    ScrollTrigger.getAll().forEach(trigger => trigger.kill());
  };
}, []);
```

### 3. Initial States
```javascript
// Set initial states untuk mencegah FOUC
gsap.set(element, { opacity: 0, y: 40 });
```

### 4. Conditional Loading
```javascript
// Check GSAP availability
if (typeof window !== 'undefined' && window.gsap) {
  // Animation code
}
```

## 🎨 Easing Functions Guide

- **power4.out**: Smooth deceleration (text reveal)
- **expo.out**: Dramatic slow-down (scaling effects)
- **elastic.out**: Bouncy overshoot (CTA buttons)
- **power2.inOut**: Balanced acceleration (hover effects)

## 🔧 Animation Timing

### Loader: 2.2s total
- Typing: 0.8s per word
- Erasing: 0.4s per word
- Logo: 1s fade + 1s scale
- Exit: 0.5s fade

### Scroll Animations: 
- Start: "top 80%"
- Duration: 0.8-1.2s
- Stagger: 0.2s

### Hover Effects:
- Duration: 0.3s
- Scale: 1.05-1.1
- Y-offset: -4 to -8px

## 🚀 CDN vs Local GSAP

### Development (CDN)
```html
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
```

### Production (Local)
```bash
npm install gsap
```

```javascript
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
gsap.registerPlugin(ScrollTrigger);
```

## 🎪 Animation Best Practices

1. **Always cleanup**: Kill ScrollTriggers on unmount
2. **Use transforms**: Avoid animating layout properties
3. **Set initial states**: Prevent flash of unstyled content
4. **Optimize for mobile**: Reduce complexity on smaller screens
5. **Progressive enhancement**: Fallback untuk browser tanpa GSAP

## 🔍 Debugging Tips

1. **Timeline inspection**: `tl.pause()` untuk debugging
2. **ScrollTrigger markers**: `markers: true` untuk visualisasi
3. **Performance monitor**: Check frame rate di DevTools
4. **Memory leaks**: Monitor ScrollTrigger instances

## 📱 Mobile Considerations

- Reduce particle count
- Simplify 3D transforms
- Shorter animation durations
- Touch-friendly hover states
- Viewport-based scaling 