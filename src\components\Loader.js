import React, { useEffect, useRef } from 'react';

const Loader = ({ onComplete }) => {
  const loaderRef = useRef(null);
  const typingRef = useRef(null);
  const logoRef = useRef(null);

  useEffect(() => {
    if (typeof window !== 'undefined' && window.gsap) {
      const { gsap } = window;
      
      // Set initial states
      gsap.set(logoRef.current, { opacity: 0, scale: 0.5 });
      gsap.set(typingRef.current, { opacity: 1 });

      // Main timeline
      const tl = gsap.timeline({
        onComplete: () => {
          if (onComplete) onComplete();
        }
      });

      // Typing sequence
      tl.to(typingRef.current, { 
        text: "Guessing...", 
        duration: 0.8,
        ease: "power2.inOut"
      })
      .to(typingRef.current, { 
        text: "", 
        duration: 0.4,
        ease: "power2.inOut"
      })
      .to(typingRef.current, { 
        text: "Tracking...", 
        duration: 0.8,
        ease: "power2.inOut"
      })
      .to(typingRef.current, { 
        text: "", 
        duration: 0.4,
        ease: "power2.inOut"
      })
      .to(typingRef.current, { 
        text: "Clarity.", 
        duration: 0.8,
        ease: "power2.inOut"
      })
      .to(logoRef.current, { 
        opacity: 1, 
        scale: 1, 
        duration: 1,
        ease: "expo.inOut"
      }, "-=0.3")
      .to([logoRef.current, typingRef.current], { 
        scale: 0.3, 
        y: -window.innerHeight * 0.4,
        duration: 1,
        ease: "expo.inOut"
      }, "+=0.5")
      .to(loaderRef.current, { 
        opacity: 0, 
        duration: 0.5,
        ease: "power2.inOut"
      });

      return () => {
        tl.kill();
      };
    }
  }, [onComplete]);

  return (
    <div 
      ref={loaderRef}
      className="fixed inset-0 z-50 bg-midnight flex items-center justify-center"
    >
      <div className="text-center">
        {/* Typing Text */}
        <div 
          ref={typingRef}
          className="font-mono text-xl text-cta-glow mb-8 h-8"
        >
        </div>
        
        {/* Logo */}
        <div 
          ref={logoRef}
          className="text-center"
        >
          <div className="text-4xl font-clash font-bold text-primary-text mb-2">
            Klikbuy
          </div>
          <div className="w-16 h-1 bg-gradient-to-r from-cta-primary to-cta-gradient-end mx-auto rounded-full"></div>
        </div>
      </div>
    </div>
  );
};

export default Loader; 