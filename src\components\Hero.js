import React, { useEffect, useRef } from 'react';

const Hero = () => {
  const heroRef = useRef(null);
  const titleRef = useRef(null);
  const subtitleRef = useRef(null);
  const ctaRef = useRef(null);

  useEffect(() => {
    if (typeof window !== 'undefined' && window.gsap) {
      const { gsap } = window;
      
      // Set initial states
      gsap.set([titleRef.current, subtitleRef.current, ctaRef.current], {
        opacity: 0,
        y: 40,
        filter: 'blur(10px)'
      });
      


      // ScrollTrigger for pinning
      gsap.registerPlugin(window.ScrollTrigger);
      
      // Pin the hero section
      window.ScrollTrigger.create({
        trigger: heroRef.current,
        start: "top top",
        end: "bottom center",
        pin: true,
        pinSpacing: false
      });

      // Staggered reveal animation
      const tl = gsap.timeline({
        scrollTrigger: {
          trigger: heroRef.current,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      });

      tl.to(titleRef.current, {
        opacity: 1,
        y: 0,
        filter: 'blur(0px)',
        duration: 1.2,
        ease: "power4.out"
      })
      .to(subtitleRef.current, {
        opacity: 1,
        y: 0,
        filter: 'blur(0px)',
        duration: 1,
        ease: "power4.out"
      }, "-=0.8")
      .to(ctaRef.current, {
        opacity: 1,
        y: 0,
        filter: 'blur(0px)',
        duration: 0.8,
        ease: "power4.out"
      }, "-=0.6");

      return () => {
        window.ScrollTrigger.getAll().forEach(trigger => trigger.kill());
      };
    }
  }, []);

  const handleCTAClick = (e) => {
    if (window.gsap) {
      window.gsap.to(e.target, {
        scale: 0.97,
        duration: 0.1,
        yoyo: true,
        repeat: 1,
        ease: "power2.inOut"
      });
    }
  };

  return (
    <section 
      ref={heroRef}
      className="min-h-screen bg-midnight relative flex items-center justify-center overflow-hidden"
    >
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-midnight via-dark-contrast to-midnight opacity-50"></div>
      
      {/* Floating Background Blobs */}
      <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-cta-primary opacity-10 rounded-full blur-3xl animate-float"></div>
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-cta-glow opacity-5 rounded-full blur-3xl animate-breath"></div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="max-w-6xl mx-auto text-center">
          {/* Main Title */}
          <h1 
            ref={titleRef}
            className="text-hero font-clash font-bold text-primary-text mb-8 leading-none tracking-tight"
          >
            <span className="block">Growth is easy to chase.</span>
            <span className="block text-cta-glow">Hard to understand.</span>
          </h1>

          {/* Subtitle */}
          <p 
            ref={subtitleRef}
            className="text-body font-inter text-secondary-text max-w-3xl mx-auto mb-12 leading-relaxed"
          >
            Klikbuy brings clarity to every promo you run — so you can lead with confidence.
          </p>

          {/* CTA Button */}
          <div ref={ctaRef}>
            <button 
              onClick={handleCTAClick}
              className="group relative px-12 py-5 bg-white text-midnight font-inter font-bold text-lg rounded-xl transition-all duration-300 hover:bg-cta-glow hover:text-midnight hover:scale-105 hover:-translate-y-1 shadow-2xl hover:shadow-white/30"
            >
              <span className="relative z-10">Join Early Access</span>
            </button>
          </div>


        </div>
      </div>
    </section>
  );
};

export default Hero; 