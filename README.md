# Klikbuy Homepage

Homepage futuristik untuk platform SaaS Klikbuy, dibangun dengan React + TailwindCSS + GSAP animations. Desain mengikuti standar branding SaaS high-end 2025 dengan tema dark mode first dan sistem aksen biru yang elegan.

## 🚀 Fitur Utama

- **Loader Interaktif**: Animasi typing effect dengan logo fade-in menggunakan GSAP timeline
- **Hero Section**: Scroll pinning dengan reveal animation dan 3D abstract model
- **Belief Section**: Layout brutalist dengan staggered text fade-in
- **Why Join Section**: Cards dengan float-in animation dan 3D rotation hover
- **Who For Section**: Flip-in cards dengan emoji dan elastic hover effects
- **Final CTA**: Scroll bounce animation dengan glow button effects
- **Responsive Design**: Optimized untuk desktop dan mobile
- **Performance**: GSAP tree-shaking dan lazy loading

## 🎨 Design System

### Warna
- **Primary Background**: `#0A0E1A` (midnight navy)
- **Section Background**: `#121829` (soft contrast dark)
- **Primary Text**: `#F4F4F4`
- **Secondary Text**: `#A0A6B3`
- **CTA Primary**: `#2146FF`
- **CTA Glow**: `#96C9FF`
- **CTA Gradient**: `linear-gradient(90deg, #2146FF, #3F80FF)`

### Typography
- **H1**: 120px, "Clash Display", sans-serif
- **Body**: 18px, "Inter", sans-serif
- **Labels**: 14px, "JetBrains Mono", monospace

### Animasi
- **Easing**: Power4.easeOut, Expo.easeInOut
- **ScrollTrigger**: GSAP untuk semua scroll-based animations
- **Micro-interactions**: Hover effects dengan scale dan glow
- **Loading**: Timeline dengan typing dan scaling effects

## 🛠️ Tech Stack

- **React.js 18.2.0** - Frontend framework
- **TailwindCSS** - Utility-first CSS framework
- **GSAP 3.12.2** - Professional animation library
- **ScrollTrigger** - Scroll-based animations
- **TextPlugin** - Text animation effects

## 📦 Setup & Installation

1. **Clone repository**
```bash
git clone <repository-url>
cd klikbuy-homepage
```

2. **Install dependencies**
```bash
npm install
# atau
yarn install
```

3. **Start development server**
```bash
npm start
# atau
yarn start
```

4. **Build for production**
```bash
npm run build
# atau
yarn build
```

## 🗂️ Struktur File

```
src/
├── components/
│   ├── Loader.js           # Fullscreen loader dengan typing animation
│   ├── Hero.js             # Hero section dengan scroll pinning
│   ├── Belief.js           # Brutalist layout dengan staggered reveal
│   ├── WhyJoin.js          # Cards dengan 3D hover effects
│   ├── WhoFor.js           # Flip-in cards dengan emoji
│   └── FinalCTA.js         # CTA section dengan bounce animation
├── App.js                  # Main component dengan navigation
└── index.js                # Entry point
```

## 🎯 Performa Optimizations

- **GSAP Tree-shaking**: Import hanya module yang dibutuhkan
- **Font preloading**: Critical fonts loaded in `<head>`
- **Lazy loading**: Non-critical components loaded sesuai kebutuhan
- **ScrollTrigger cleanup**: Proper cleanup untuk mencegah memory leaks
- **Transform-gpu**: Hardware acceleration untuk smooth animations

## 🎮 Interactive Elements

### Loader Animation
- Typing sequence: "Guessing..." → "Tracking..." → "Clarity."
- Logo fade-in dan scale
- Transition ke navbar position

### Hover Effects
- **Buttons**: Scale + glow + gradient background
- **Cards**: Lift dengan shadow + icon rotation
- **Navigation**: Color transitions

### Scroll Animations
- **Hero**: Pinned section dengan staggered reveal
- **Sections**: Fade-in dengan y-offset
- **Elements**: Staggered animations dengan delay

## 🚀 Deployment

Homepage siap untuk production dengan:
- Static build optimization
- CDN-ready assets
- SEO-friendly structure
- Fast loading times

## 🎨 Customization

### Mengubah Warna
Edit file `public/index.html` pada bagian `tailwind.config`:

```javascript
colors: {
  'midnight': '#0A0E1A',        // Background utama
  'cta-primary': '#2146FF',     // Warna CTA
  'cta-glow': '#96C9FF',        // Warna glow effect
  // ... tambah warna custom
}
```

### Mengubah Animasi
Edit timing dan easing di setiap komponen:

```javascript
gsap.to(element, {
  duration: 1.2,              // Durasi
  ease: "power4.out",         // Easing function
  stagger: 0.2                // Delay antar element
});
```

## 📱 Responsive Design

- **Desktop**: Full experience dengan semua animasi
- **Tablet**: Adapted layout dengan simplified animations
- **Mobile**: Optimized touch interactions

## 🔧 Browser Support

- **Chrome**: 90+
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+

## 📄 License

© 2025 Klikbuy. Built for clarity.

---

**Catatan**: Homepage ini menggunakan GSAP CDN untuk kemudahan development. Untuk production, pertimbangkan hosting GSAP secara lokal untuk performa optimal. 