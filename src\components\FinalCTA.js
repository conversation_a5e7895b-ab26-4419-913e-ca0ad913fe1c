import React, { useEffect, useRef } from 'react';

const FinalCTA = () => {
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const subtitleRef = useRef(null);
  const ctaRef = useRef(null);
  const backgroundRef = useRef(null);

  useEffect(() => {
    if (typeof window !== 'undefined' && window.gsap) {
      const { gsap } = window;
      
      // Set initial states
      gsap.set([titleRef.current, subtitleRef.current], {
        opacity: 0,
        y: 50
      });
      
      gsap.set(ctaRef.current, {
        opacity: 0,
        scale: 0.8,
        y: 30
      });
      
      gsap.set(backgroundRef.current, {
        scale: 0.9,
        opacity: 0
      });

      // ScrollTrigger animation
      gsap.registerPlugin(window.ScrollTrigger);
      
      const tl = gsap.timeline({
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      });

      // Background scale in
      tl.to(backgroundRef.current, {
        scale: 1,
        opacity: 1,
        duration: 1.5,
        ease: "power2.out"
      })
      // Title animation
      .to(titleRef.current, {
        opacity: 1,
        y: 0,
        duration: 1.2,
        ease: "power4.out"
      }, "-=1")
      // Subtitle animation
      .to(subtitleRef.current, {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: "power4.out"
      }, "-=0.8")
      // CTA bounce in with elastic
      .to(ctaRef.current, {
        opacity: 1,
        scale: 1,
        y: 0,
        duration: 1.2,
        ease: "elastic.out(1, 0.6)"
      }, "-=0.6");

      return () => {
        window.ScrollTrigger.getAll().forEach(trigger => trigger.kill());
      };
    }
  }, []);

  const handleCTAClick = (e) => {
    if (window.gsap) {
      window.gsap.to(e.target, {
        scale: 0.97,
        duration: 0.1,
        yoyo: true,
        repeat: 1,
        ease: "power2.inOut"
      });
    }
  };

  const handleCTAHover = (e, isEntering) => {
    if (window.gsap) {
      if (isEntering) {
        window.gsap.to(e.target, {
          scale: 1.05,
          y: -4,
          boxShadow: "0 20px 40px rgba(150, 201, 255, 0.4)",
          duration: 0.3,
          ease: "power2.out"
        });
      } else {
        window.gsap.to(e.target, {
          scale: 1,
          y: 0,
          boxShadow: "0 10px 20px rgba(150, 201, 255, 0.2)",
          duration: 0.3,
          ease: "power2.out"
        });
      }
    }
  };

  return (
    <section 
      ref={sectionRef}
      className="min-h-screen bg-midnight py-20 relative overflow-hidden flex items-center justify-center"
    >
      {/* Background Elements */}
      <div 
        ref={backgroundRef}
        className="absolute inset-0"
      >
        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-dark-contrast via-midnight to-dark-contrast opacity-80"></div>
        
        {/* Floating orbs */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-cta-primary opacity-10 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-cta-glow opacity-15 rounded-full blur-3xl animate-breath"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-cta-gradient-end opacity-8 rounded-full blur-3xl animate-float" style={{animationDelay: '1s'}}></div>
        
        {/* Grid pattern */}
        <div className="absolute inset-0 opacity-5" 
             style={{
               backgroundImage: `radial-gradient(circle at 25% 25%, rgba(33, 70, 255, 0.3) 0%, transparent 50%), 
                                 radial-gradient(circle at 75% 75%, rgba(150, 201, 255, 0.3) 0%, transparent 50%)`
             }}>
        </div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          {/* Main Message */}
          <h2 
            ref={titleRef}
            className="text-6xl font-clash font-bold text-primary-text mb-8 leading-tight"
          >
            You don't need more data.
            <br />
            <span className="text-cta-glow">You need clarity.</span>
          </h2>

          {/* Supporting Text */}
          <p 
            ref={subtitleRef}
            className="text-xl font-inter text-secondary-text mb-12 max-w-2xl mx-auto leading-relaxed"
          >
            Join the brands who grow with confidence.
          </p>

                     {/* CTA Button */}
           <div ref={ctaRef}>
             <button 
               onClick={handleCTAClick}
               onMouseEnter={(e) => handleCTAHover(e, true)}
               onMouseLeave={(e) => handleCTAHover(e, false)}
               className="group relative px-16 py-6 bg-white text-midnight font-inter font-bold text-xl rounded-2xl transition-all duration-300 hover:bg-cta-glow hover:text-midnight hover:scale-105 transform-gpu shadow-2xl hover:shadow-white/40"
             >
               <span className="relative z-10 flex items-center space-x-3">
                 <span>Request Early Access</span>
                 <svg 
                   className="w-6 h-6 transform group-hover:translate-x-1 transition-transform duration-300" 
                   fill="none" 
                   stroke="currentColor" 
                   viewBox="0 0 24 24"
                 >
                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                 </svg>
               </span>
             </button>
           </div>

                     {/* Bottom accent */}
           <div className="mt-16 flex justify-center">
             <div className="flex items-center space-x-4">
               <div className="w-24 h-px bg-gradient-to-r from-transparent to-cta-primary"></div>
               <div className="w-24 h-px bg-gradient-to-l from-transparent to-cta-glow"></div>
             </div>
           </div>

          {/* Trust message */}
          <div className="mt-8">
            <p className="text-sm font-mono text-secondary-text/60">
              Join 500+ forward-thinking brands
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FinalCTA;