import React, { useState, useEffect } from 'react';
import Loader from './components/Loader';
import Hero from './components/Hero';
import Belief from './components/Belief';
import WhyJoin from './components/WhyJoin';
import WhoFor from './components/WhoFor';
import FinalCTA from './components/FinalCTA';

function App() {
  const [isLoading, setIsLoading] = useState(true);

  const handleLoadingComplete = () => {
    setIsLoading(false);
  };

  useEffect(() => {
    // Prevent scroll during loading
    if (isLoading) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isLoading]);

  useEffect(() => {
    // Register GSAP plugins when component mounts
    if (typeof window !== 'undefined' && window.gsap) {
      window.gsap.registerPlugin(window.ScrollTrigger);
    }

    // Cleanup function
    return () => {
      if (typeof window !== 'undefined' && window.ScrollTrigger) {
        window.ScrollTrigger.getAll().forEach(trigger => trigger.kill());
      }
    };
  }, []);

  return (
    <div className="App">
      {/* Loader */}
      {isLoading && <Loader onComplete={handleLoadingComplete} />}
      
      {/* Main Content */}
      <div className={`transition-opacity duration-500 ${isLoading ? 'opacity-0' : 'opacity-100'}`}>

        {/* Main Sections */}
        <main>
          <section id="hero">
            <Hero />
          </section>
          
          <section id="belief">
            <Belief />
          </section>
          
          <section id="why-join">
            <WhyJoin />
          </section>
          
          <section id="who-for">
            <WhoFor />
          </section>
          
          <section id="final-cta">
            <FinalCTA />
          </section>
        </main>

        {/* Footer */}
        <footer className="bg-dark-contrast border-t border-white/10 py-12">
          <div className="container mx-auto px-6">
            <div className="grid md:grid-cols-4 gap-8">
              {/* Brand */}
              <div className="md:col-span-2">
                <div className="flex items-center space-x-2 mb-4">
                  <div className="text-2xl font-clash font-bold text-primary-text">
                    Klikbuy
                  </div>
                  <div className="w-8 h-0.5 bg-gradient-to-r from-cta-primary to-cta-gradient-end rounded-full"></div>
                </div>
                <p className="text-secondary-text font-inter max-w-md">
                  Bringing clarity to every promo you run. Lead with confidence, not guesses.
                </p>
              </div>
              
              {/* Links */}
              <div>
                <h4 className="text-primary-text font-clash font-semibold mb-4">Product</h4>
                <ul className="space-y-2">
                  <li><button className="text-secondary-text hover:text-cta-glow transition-colors duration-300 font-inter text-left">Features</button></li>
                  <li><button className="text-secondary-text hover:text-cta-glow transition-colors duration-300 font-inter text-left">Pricing</button></li>
                  <li><button className="text-secondary-text hover:text-cta-glow transition-colors duration-300 font-inter text-left">API</button></li>
                </ul>
              </div>
              
              <div>
                <h4 className="text-primary-text font-clash font-semibold mb-4">Company</h4>
                <ul className="space-y-2">
                  <li><button className="text-secondary-text hover:text-cta-glow transition-colors duration-300 font-inter text-left">About</button></li>
                  <li><button className="text-secondary-text hover:text-cta-glow transition-colors duration-300 font-inter text-left">Blog</button></li>
                  <li><button className="text-secondary-text hover:text-cta-glow transition-colors duration-300 font-inter text-left">Contact</button></li>
                </ul>
              </div>
            </div>
            
            {/* Bottom */}
            <div className="border-t border-white/10 mt-8 pt-8 flex md:flex-row flex-col items-center justify-between">
              <p className="text-secondary-text font-mono text-sm">
                © 2025 Klikbuy. Built for clarity.
              </p>
              <div className="mt-4 md:mt-0">
                <p className="text-secondary-text font-mono text-xs">
                  Experience the future of clarity.
                </p>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
}

export default App; 