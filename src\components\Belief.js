import React, { useEffect, useRef } from 'react';

const Belief = () => {
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const beliefItemsRef = useRef([]);

  const beliefTexts = [
    "We believe every business — big or small — deserves to grow with clarity.",
    "No more campaigns that feel like guesses.",
    "No more decisions made in the dark.",
    "Klikbuy exists to bring truth into your growth."
  ];

  useEffect(() => {
    if (typeof window !== 'undefined' && window.gsap) {
      const { gsap } = window;
      
      // Set initial states
      gsap.set(titleRef.current, {
        opacity: 0,
        y: 60
      });
      
      gsap.set(beliefItemsRef.current, {
        opacity: 0,
        y: 40,
        x: -20
      });

      // ScrollTrigger animation
      gsap.registerPlugin(window.ScrollTrigger);
      
      const tl = gsap.timeline({
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      });

      // Animate title first
      tl.to(titleRef.current, {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: "power4.out"
      })
      // Then stagger the belief items
      .to(beliefItemsRef.current, {
        opacity: 1,
        y: 0,
        x: 0,
        duration: 0.8,
        stagger: 0.2,
        ease: "power4.out"
      }, "-=0.5");

      return () => {
        window.ScrollTrigger.getAll().forEach(trigger => trigger.kill());
      };
    }
  }, []);

  const addToRefs = (el, index) => {
    if (el) {
      beliefItemsRef.current[index] = el;
    }
  };

  return (
    <section 
      ref={sectionRef}
      className="min-h-screen bg-dark-contrast py-20 relative overflow-hidden"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-cta-primary to-transparent"></div>
        <div className="absolute bottom-0 right-0 w-1/2 h-1/2 bg-gradient-to-tl from-cta-glow to-transparent"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="max-w-5xl mx-auto">
          {/* Section Title */}
          <div 
            ref={titleRef}
            className="mb-16 text-center"
          >
            <h2 className="text-6xl font-clash font-bold text-primary-text mb-4">
              What We Believe
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-cta-primary to-cta-gradient-end mx-auto rounded-full"></div>
          </div>

          {/* Belief Items - Brutalist Layout */}
          <div className="space-y-8">
            {beliefTexts.map((text, index) => (
              <div
                key={index}
                ref={(el) => addToRefs(el, index)}
                className={`
                  relative p-8 border-l-4 border-cta-primary bg-midnight/50 backdrop-blur-sm
                  ${index % 2 === 0 ? 'ml-0 mr-16' : 'ml-16 mr-0'}
                  hover:border-cta-glow hover:bg-midnight/70 transition-all duration-300
                  group cursor-default
                `}
              >
                {/* Block accent */}
                <div className={`
                  absolute top-0 w-2 h-full bg-cta-primary group-hover:bg-cta-glow transition-all duration-300
                  ${index % 2 === 0 ? '-left-1' : '-right-1'}
                `}></div>
                
                {/* Content */}
                <div className="relative">
                  <p className="text-body font-inter text-primary-text leading-relaxed">
                    {text}
                  </p>
                  
                  {/* Number indicator */}
                  <div className={`
                    absolute -top-4 w-8 h-8 bg-cta-primary text-midnight font-mono font-bold 
                    text-sm flex items-center justify-center rounded group-hover:bg-cta-glow
                    transition-all duration-300
                    ${index % 2 === 0 ? '-left-12' : '-right-12'}
                  `}>
                    {index + 1}
                  </div>
                </div>

                {/* Hover effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-cta-primary/5 to-cta-glow/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
            ))}
          </div>


        </div>
      </div>
    </section>
  );
};

export default Belief; 