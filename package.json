{"name": "klikbuy-homepage", "version": "1.0.0", "private": true, "description": "Homepage futuristik untuk platform SaaS Klikbuy, dibangun dengan React + TailwindCSS + GSAP animations. <PERSON><PERSON> mengi<PERSON> standar branding SaaS high-end 2025 dengan tema dark mode first dan sistem aksen biru yang elegan.", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "^5.0.1", "gsap": "^3.12.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "keywords": ["react", "gsap", "tailwindcss", "saas", "homepage", "klikbuy"], "author": "Klikbuy Team", "license": "MIT"}