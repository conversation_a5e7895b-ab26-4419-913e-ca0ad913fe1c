import React, { useEffect, useRef } from 'react';

const WhoFor = () => {
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const cardsRef = useRef([]);

  const personas = [
    {
      emoji: "🏪",
      title: "Small Business Owners",
      subtitle: "Tired of Guessing",
      description: "Stop shooting in the dark. Get clear insights on what actually works for your business growth."
    },
    {
      emoji: "🚀", 
      title: "Scale-up Founders",
      subtitle: "Ready to Act Faster",
      description: "Move beyond gut feelings. Make data-driven decisions that accelerate your scaling journey."
    },
    {
      emoji: "🎯",
      title: "Enterprise Leaders", 
      subtitle: "Seeking Clarity",
      description: "Transform complex data into actionable insights. Lead your team with confidence and precision."
    }
  ];

  useEffect(() => {
    if (typeof window !== 'undefined' && window.gsap) {
      const { gsap } = window;
      
      // Set initial states
      gsap.set(titleRef.current, {
        opacity: 0,
        y: 50
      });
      
      gsap.set(cardsRef.current, {
        opacity: 0,
        y: 60,
        rotationX: -20,
        scale: 0.9
      });

      // ScrollTrigger animation
      gsap.registerPlugin(window.ScrollTrigger);
      
      const tl = gsap.timeline({
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      });

      // Animate title first
      tl.to(titleRef.current, {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: "power4.out"
      })
      // Then flip-in the cards
      .to(cardsRef.current, {
        opacity: 1,
        y: 0,
        rotationX: 0,
        scale: 1,
        duration: 1.2,
        stagger: 0.2,
        ease: "expo.out"
      }, "-=0.5");

      return () => {
        window.ScrollTrigger.getAll().forEach(trigger => trigger.kill());
      };
    }
  }, []);

  const addToRefs = (el, index) => {
    if (el) {
      cardsRef.current[index] = el;
    }
  };

  const handleCardHover = (e, isEntering) => {
    if (window.gsap) {
      const card = e.currentTarget;
      const emoji = card.querySelector('.persona-emoji');
      
      if (isEntering) {
        window.gsap.to(card, {
          y: -12,
          scale: 1.02,
          boxShadow: "0 25px 50px rgba(150, 201, 255, 0.25)",
          duration: 0.4,
          ease: "power2.out"
        });
        
        window.gsap.to(emoji, {
          scale: 1.15,
          rotation: 10,
          duration: 0.4,
          ease: "elastic.out(1, 0.3)"
        });
      } else {
        window.gsap.to(card, {
          y: 0,
          scale: 1,
          boxShadow: "0 15px 30px rgba(150, 201, 255, 0.15)",
          duration: 0.4,
          ease: "power2.out"
        });
        
        window.gsap.to(emoji, {
          scale: 1,
          rotation: 0,
          duration: 0.4,
          ease: "elastic.out(1, 0.3)"
        });
      }
    }
  };

  return (
    <section 
      ref={sectionRef}
      className="min-h-screen bg-dark-contrast py-20 relative overflow-hidden"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0">
        {/* Animated grid pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-0 left-0 w-full h-full" 
               style={{
                 backgroundImage: `radial-gradient(circle at 20% 50%, rgba(33, 70, 255, 0.3) 0%, transparent 50%), 
                                   radial-gradient(circle at 80% 20%, rgba(150, 201, 255, 0.3) 0%, transparent 50%)`,
               }}>
          </div>
        </div>
        
        {/* Floating elements */}
        <div className="absolute top-1/3 left-1/4 w-48 h-48 bg-cta-primary opacity-10 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-1/3 right-1/4 w-64 h-64 bg-cta-glow opacity-8 rounded-full blur-3xl animate-breath"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div 
            ref={titleRef}
            className="text-center mb-16"
          >
            <h2 className="text-6xl font-clash font-bold text-primary-text mb-6">
              Who It's For
            </h2>
            <p className="text-body font-inter text-secondary-text">
              Klikbuy was built for:
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-cta-primary to-cta-gradient-end mx-auto mt-6 rounded-full"></div>
          </div>

          {/* Persona Cards */}
          <div className="grid md:grid-cols-3 gap-8">
            {personas.map((persona, index) => (
              <div
                key={index}
                ref={(el) => addToRefs(el, index)}
                className="group relative p-8 bg-midnight border border-white/10 rounded-2xl backdrop-blur-sm hover:border-cta-glow/50 transition-all duration-300 cursor-pointer transform-gpu"
                onMouseEnter={(e) => handleCardHover(e, true)}
                onMouseLeave={(e) => handleCardHover(e, false)}
                style={{
                  boxShadow: "0 15px 30px rgba(150, 201, 255, 0.15)",
                  perspective: "1000px"
                }}
              >
                {/* Emoji */}
                <div className="text-center mb-6">
                  <div className="persona-emoji text-7xl inline-block transform-gpu">
                    {persona.emoji}
                  </div>
                </div>

                {/* Content */}
                <div className="text-center">
                  <h3 className="text-2xl font-clash font-bold text-primary-text mb-2">
                    {persona.title}
                  </h3>
                  <div className="text-cta-glow font-mono text-sm mb-4 tracking-wide">
                    {persona.subtitle}
                  </div>
                  <p className="text-body font-inter text-secondary-text leading-relaxed">
                    {persona.description}
                  </p>
                </div>

                {/* Card glow effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-cta-primary/10 to-cta-glow/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl pointer-events-none"></div>
                
                {/* Top accent */}
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-0 h-1 bg-gradient-to-r from-cta-primary to-cta-gradient-end group-hover:w-3/4 transition-all duration-700 rounded-b-full"></div>
                
                {/* Side glow */}
                <div className="absolute inset-y-0 -left-px w-1 bg-gradient-to-b from-transparent via-cta-glow to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-full"></div>
              </div>
            ))}
          </div>

          {/* Bottom message */}
          <div className="mt-16 text-center">
            <div className="inline-flex items-center space-x-3 px-6 py-3 bg-midnight/50 border border-white/10 rounded-full backdrop-blur-sm">
              <span className="text-secondary-text font-mono text-sm">
                Which one describes you?
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhoFor; 