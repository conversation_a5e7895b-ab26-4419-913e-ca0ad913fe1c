import React, { useEffect, useRef } from 'react';

const WhyJoin = () => {
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const subtitleRef = useRef(null);
  const itemsRef = useRef([]);

  const reasons = [
    {
      icon: "📊", // Chart icon
      title: "Track Promo Performance",
      description: "They want to know which promos drive traffic"
    },
    {
      icon: "⚡", // Lightning icon  
      title: "Act Faster, Waste Less",
      description: "They want to act faster, waste less"
    },
    {
      icon: "🎯", // Target icon
      title: "Lead with Confidence", 
      description: "They want to lead — not guess"
    }
  ];

  useEffect(() => {
    if (typeof window !== 'undefined' && window.gsap) {
      const { gsap } = window;
      
      // Set initial states
      gsap.set([titleRef.current, subtitleRef.current], {
        opacity: 0,
        y: 40
      });
      
      gsap.set(itemsRef.current, {
        opacity: 0,
        y: 60,
        scale: 0.8
      });

      // ScrollTrigger animation
      gsap.registerPlugin(window.ScrollTrigger);
      
      const tl = gsap.timeline({
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      });

      // Animate title and subtitle
      tl.to(titleRef.current, {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: "power4.out"
      })
      .to(subtitleRef.current, {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: "power4.out"
      }, "-=0.6")
      // Stagger the items with float-in effect
      .to(itemsRef.current, {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 1,
        stagger: 0.2,
        ease: "expo.out"
      }, "-=0.4");

      return () => {
        window.ScrollTrigger.getAll().forEach(trigger => trigger.kill());
      };
    }
  }, []);

  const addToRefs = (el, index) => {
    if (el) {
      itemsRef.current[index] = el;
    }
  };

  const handleItemHover = (e, isEntering) => {
    if (window.gsap) {
      const iconEl = e.currentTarget.querySelector('.reason-icon');
      
      if (isEntering) {
        window.gsap.to(iconEl, {
          scale: 1.1,
          rotationY: 15,
          rotationX: 5,
          duration: 0.3,
          ease: "power2.out"
        });
        
        window.gsap.to(e.currentTarget, {
          y: -8,
          boxShadow: "0 20px 40px rgba(150, 201, 255, 0.2)",
          duration: 0.3,
          ease: "power2.out"
        });
      } else {
        window.gsap.to(iconEl, {
          scale: 1,
          rotationY: 0,
          rotationX: 0,
          duration: 0.3,
          ease: "power2.out"
        });
        
        window.gsap.to(e.currentTarget, {
          y: 0,
          boxShadow: "0 10px 20px rgba(150, 201, 255, 0.1)",
          duration: 0.3,
          ease: "power2.out"
        });
      }
    }
  };

  return (
    <section 
      ref={sectionRef}
      className="min-h-screen bg-midnight py-20 relative overflow-hidden"
    >
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 right-1/4 w-72 h-72 bg-cta-primary opacity-5 rounded-full blur-3xl animate-breath"></div>
        <div className="absolute bottom-1/3 left-1/3 w-96 h-96 bg-cta-glow opacity-3 rounded-full blur-3xl animate-float"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <h2 
              ref={titleRef}
              className="text-6xl font-clash font-bold text-primary-text mb-6"
            >
              Why Brands Join Us
            </h2>
            <p 
              ref={subtitleRef}
              className="text-body font-inter text-secondary-text max-w-2xl mx-auto"
            >
              Not because we're a tool. Because we're a clarity partner.
            </p>
          </div>

          {/* Reasons Grid */}
          <div className="grid md:grid-cols-3 gap-8">
            {reasons.map((reason, index) => (
              <div
                key={index}
                ref={(el) => addToRefs(el, index)}
                className="group p-8 bg-dark-contrast border border-white/10 rounded-xl backdrop-blur-sm hover:border-cta-glow/50 transition-all duration-300 cursor-pointer"
                onMouseEnter={(e) => handleItemHover(e, true)}
                onMouseLeave={(e) => handleItemHover(e, false)}
                style={{
                  boxShadow: "0 10px 20px rgba(150, 201, 255, 0.1)"
                }}
              >
                {/* Icon */}
                <div className="mb-6 flex justify-center">
                  <div className="reason-icon text-6xl transform-gpu perspective-1000">
                    {reason.icon}
                  </div>
                </div>

                {/* Content */}
                <div className="text-center">
                  <h3 className="text-2xl font-clash font-semibold text-primary-text mb-4">
                    {reason.title}
                  </h3>
                  <p className="text-body font-inter text-secondary-text leading-relaxed">
                    {reason.description}
                  </p>
                </div>

                {/* Hover gradient */}
                <div className="absolute inset-0 bg-gradient-to-br from-cta-primary/5 to-cta-glow/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl pointer-events-none"></div>
                
                {/* Bottom accent line */}
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-0 h-1 bg-gradient-to-r from-cta-primary to-cta-gradient-end group-hover:w-full transition-all duration-500 rounded-full"></div>
              </div>
            ))}
          </div>

          {/* Bottom CTA hint */}
          <div className="mt-16 text-center">
            <div className="inline-flex items-center space-x-2 text-secondary-text font-mono text-sm">
              <span>Ready to experience clarity?</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhyJoin; 